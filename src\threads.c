/**
 * threads.c: 通用线程相关例程集合
 *
 *
 *

 */

#define IN_LIBXML
#include "libxml.h"

#include <string.h>
#include <stdarg.h>
#include <stdlib.h>

#include <libxml/threads.h>
#include <libxml/parser.h>
#ifdef LIBXML_CATALOG_ENABLED
#include <libxml/catalog.h>
#endif
#ifdef LIBXML_RELAXNG_ENABLED
#include <libxml/relaxng.h>
#endif
#ifdef LIBXML_SCHEMAS_ENABLED
#include <libxml/xmlschemastypes.h>
#endif

#if defined(SOLARIS)
#include <note.h>
#endif

#include "private/cata.h"
#include "private/dict.h"
#include "private/enc.h"
#include "private/error.h"
#include "private/globals.h"
#include "private/io.h"
#include "private/memory.h"
#include "private/threads.h"
#include "private/xpath.h"

/*
 *  此模块仍然使用 malloc/free 而不是 xmlMalloc/xmlFree
 *       以避免一些疯狂的情况，因为 xmlMalloc/xmlFree 实际上可能
 *       托管在需要它们进行分配的已分配块上...
 */

static xmlRMutex xmlLibraryLock;

/**
 * 初始化一个互斥锁。
 *
 * @param mutex  互斥锁
 */
void
xmlInitMutex(xmlMutexPtr mutex)
{
#ifdef HAVE_POSIX_THREADS
    pthread_mutex_init(&mutex->lock, NULL);
#elif defined HAVE_WIN32_THREADS
    InitializeCriticalSection(&mutex->cs);
#else
    (void) mutex;
#endif
}

xmlMutexPtr
xmlNewMutex(void)
{
    xmlMutexPtr tok;

    tok = malloc(sizeof(xmlMutex));
    if (tok == NULL)
        return (NULL);
    xmlInitMutex(tok);
    return (tok);
}

/**
 * 回收与互斥锁关联的资源。
 *
 * @param mutex  简单互斥锁
 */
void
xmlCleanupMutex(xmlMutexPtr mutex)
{
#ifdef HAVE_POSIX_THREADS
    pthread_mutex_destroy(&mutex->lock);
#elif defined HAVE_WIN32_THREADS
    DeleteCriticalSection(&mutex->cs);
#else
    (void) mutex;
#endif
}

/**
 * 释放一个互斥锁。
 *
 * @param tok  简单互斥锁
 */
void
xmlFreeMutex(xmlMutexPtr tok)
{
    if (tok == NULL)
        return;

    xmlCleanupMutex(tok);
    free(tok);
}

void
xmlMutexLock(xmlMutexPtr tok)
{
    if (tok == NULL)
        return;
#ifdef HAVE_POSIX_THREADS
    /*
     * 这假设在持有锁时 __libc_single_threaded 不会改变。
     */
    pthread_mutex_lock(&tok->lock);
#elif defined HAVE_WIN32_THREADS
    EnterCriticalSection(&tok->cs);
#endif

}

void
xmlMutexUnlock(xmlMutexPtr tok)
{
    if (tok == NULL)
        return;
#ifdef HAVE_POSIX_THREADS
    pthread_mutex_unlock(&tok->lock);
#elif defined HAVE_WIN32_THREADS
    LeaveCriticalSection(&tok->cs);
#endif
}

/**
 * 初始化互斥锁。
 *
 * @param tok  互斥锁
 */
void
xmlInitRMutex(xmlRMutexPtr tok) {
    (void) tok;

#ifdef HAVE_POSIX_THREADS
    pthread_mutex_init(&tok->lock, NULL);
    tok->held = 0;
    tok->waiters = 0;
    pthread_cond_init(&tok->cv, NULL);
#elif defined HAVE_WIN32_THREADS
    InitializeCriticalSection(&tok->cs);
#endif
}

/**
 * xmlRNewMutex() 用于分配一个可重入互斥锁，用于
 * 同步数据访问。token_r 是一个可重入锁，因此对于
 * 同步访问可能以递归方式操作的数据结构很有用。
 *
 * @returns 新的可重入互斥锁指针，出错时返回 NULL
 */
xmlRMutexPtr
xmlNewRMutex(void)
{
    xmlRMutexPtr tok;

    tok = malloc(sizeof(xmlRMutex));
    if (tok == NULL)
        return (NULL);
    xmlInitRMutex(tok);
    return (tok);
}

/**
 * 清理互斥锁。
 *
 * @param tok  互斥锁
 */
void
xmlCleanupRMutex(xmlRMutexPtr tok) {
    (void) tok;

#ifdef HAVE_POSIX_THREADS
    pthread_mutex_destroy(&tok->lock);
    pthread_cond_destroy(&tok->cv);
#elif defined HAVE_WIN32_THREADS
    DeleteCriticalSection(&tok->cs);
#endif
}

/**
 * xmlRFreeMutex() 用于回收与可重入互斥锁关联的资源。
 *
 * @param tok  可重入互斥锁
 */
void
xmlFreeRMutex(xmlRMutexPtr tok)
{
    if (tok == NULL)
        return;
    xmlCleanupRMutex(tok);
    free(tok);
}

void
xmlRMutexLock(xmlRMutexPtr tok)
{
    if (tok == NULL)
        return;
#ifdef HAVE_POSIX_THREADS
    pthread_mutex_lock(&tok->lock);
    if (tok->held) {
        if (pthread_equal(tok->tid, pthread_self())) {
            tok->held++;
            pthread_mutex_unlock(&tok->lock);
            return;
        } else {
            tok->waiters++;
            while (tok->held)
                pthread_cond_wait(&tok->cv, &tok->lock);
            tok->waiters--;
        }
    }
    tok->tid = pthread_self();
    tok->held = 1;
    pthread_mutex_unlock(&tok->lock);
#elif defined HAVE_WIN32_THREADS
    EnterCriticalSection(&tok->cs);
#endif
}

void
xmlRMutexUnlock(xmlRMutexPtr tok ATTRIBUTE_UNUSED)
{
    if (tok == NULL)
        return;
#ifdef HAVE_POSIX_THREADS
    pthread_mutex_lock(&tok->lock);
    tok->held--;
    if (tok->held == 0) {
        if (tok->waiters)
            pthread_cond_signal(&tok->cv);
        memset(&tok->tid, 0, sizeof(tok->tid));
    }
    pthread_mutex_unlock(&tok->lock);
#elif defined HAVE_WIN32_THREADS
    LeaveCriticalSection(&tok->cs);
#endif
}

/************************************************************************
 *									*
 *			库范围的线程接口				*
 *									*
 ************************************************************************/

void
xmlLockLibrary(void)
{
    xmlRMutexLock(&xmlLibraryLock);
}

void
xmlUnlockLibrary(void)
{
    xmlRMutexUnlock(&xmlLibraryLock);
}

/**
 * @deprecated xmlInitParser() 的别名。
 */
void
xmlInitThreads(void)
{
    xmlInitParser();
}

/**
 * @deprecated 此函数是空操作。调用 xmlCleanupParser()
 * 来释放全局状态，但请查看那里的警告。xmlCleanupParser()
 * 应该只在程序退出时调用一次。在大多数情况下，您根本
 * 不需要调用清理函数。
 */
void
xmlCleanupThreads(void)
{
}

static void
xmlInitThreadsInternal(void) {
    xmlInitRMutex(&xmlLibraryLock);
}

static void
xmlCleanupThreadsInternal(void) {
    xmlCleanupRMutex(&xmlLibraryLock);
}

/************************************************************************
 *									*
 *			库范围的初始化				*
 *									*
 ************************************************************************/

static int xmlParserInitialized = 0;

#ifdef HAVE_POSIX_THREADS
static pthread_once_t onceControl = PTHREAD_ONCE_INIT;
#elif defined HAVE_WIN32_THREADS
static INIT_ONCE onceControl = INIT_ONCE_STATIC_INIT;
#else
static int onceControl = 0;
#endif

static void
xmlInitParserInternal(void) {
    /*
     * 注意初始化代码不能进行内存分配。
     */
    xmlInitRandom(); /* xmlInitGlobalsInternal 需要 */
    xmlInitMemoryInternal();
    xmlInitThreadsInternal();
    xmlInitGlobalsInternal();
    xmlInitDictInternal();
    xmlInitEncodingInternal();
#if defined(LIBXML_XPATH_ENABLED)
    xmlInitXPathInternal();
#endif
    xmlInitIOCallbacks();
#ifdef LIBXML_CATALOG_ENABLED
    xmlInitCatalogInternal();
#endif

    xmlParserInitialized = 1;
}

#if defined(HAVE_WIN32_THREADS)
static BOOL WINAPI
xmlInitParserWinWrapper(INIT_ONCE *initOnce ATTRIBUTE_UNUSED,
                        void *parameter ATTRIBUTE_UNUSED,
                        void **context ATTRIBUTE_UNUSED) {
    xmlInitParserInternal();
    return(TRUE);
}
#endif

/**
 * XML 解析器的初始化函数。
 *
 * 对于较旧的版本，建议在多线程程序中使用库之前
 * 从主线程调用此函数一次。
 *
 * 从 2.14.0 开始，线程之间没有区别。应该
 * 不需要调用此函数。
 */
void
xmlInitParser(void) {
#ifdef HAVE_POSIX_THREADS
    pthread_once(&onceControl, xmlInitParserInternal);
#elif defined(HAVE_WIN32_THREADS)
    InitOnceExecuteOnce(&onceControl, xmlInitParserWinWrapper, NULL, NULL);
#else
    if (onceControl == 0) {
        xmlInitParserInternal();
        onceControl = 1;
    }
#endif
}

/**
 * 此函数的命名有些误导性。它不清理解析器状态，
 * 而是清理库本身分配的全局内存。
 */
void
xmlCleanupParser(void) {
    /*
     * 不幸的是，一些用户在 2.9.11 之前的版本中调用此函数
     * 来修复卸载时的内存泄漏。这可能导致库被重新初始化，
     * 因此必须支持这种用例。
     */
    if (!xmlParserInitialized)
        return;

    xmlCleanupCharEncodingHandlers();
#ifdef LIBXML_CATALOG_ENABLED
    xmlCatalogCleanup();
    xmlCleanupCatalogInternal();
#endif
#ifdef LIBXML_SCHEMAS_ENABLED
    xmlSchemaCleanupTypes();
#endif
#ifdef LIBXML_RELAXNG_ENABLED
    xmlRelaxNGCleanupTypes();
#endif

    xmlCleanupDictInternal();
    xmlCleanupRandom();
    xmlCleanupGlobalsInternal();
    xmlCleanupThreadsInternal();

    /*
     * 必须在所有调用 xmlFree 的清理函数之后，
     * xmlFree 在调试模式下使用 xmlMemMutex。
     */
    xmlCleanupMemoryInternal();

    xmlParserInitialized = 0;

    /*
     * 这有点粗糙，但应该能使重新初始化工作。
     */
#ifdef HAVE_POSIX_THREADS
    {
        pthread_once_t tmp = PTHREAD_ONCE_INIT;
        memcpy(&onceControl, &tmp, sizeof(tmp));
    }
#elif defined(HAVE_WIN32_THREADS)
    {
        INIT_ONCE tmp = INIT_ONCE_STATIC_INIT;
        memcpy(&onceControl, &tmp, sizeof(tmp));
    }
#else
    onceControl = 0;
#endif
}

#if defined(HAVE_FUNC_ATTRIBUTE_DESTRUCTOR) && \
    !defined(LIBXML_THREAD_ALLOC_ENABLED) && \
    !defined(LIBXML_STATIC) && \
    !defined(_WIN32)
static void
ATTRIBUTE_DESTRUCTOR
xmlDestructor(void) {
    /*
     * 在析构函数中调用自定义释放函数可能会导致问题，
     * 例如与 Nokogiri 一起使用时。
     */
    if (xmlFree == free)
        xmlCleanupParser();
}
#endif
